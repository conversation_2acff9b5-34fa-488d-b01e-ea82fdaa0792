'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ody,
  CardHeader,
  <PERSON>ing,
  Text,
  VStack,
  HStack,
  <PERSON>ert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Progress,
  Code,
  Container,
  SimpleGrid,
  Flex,
} from '@chakra-ui/react';
import { usePrinterTest } from '@/hooks/usePrinterTest';
import PrinterTroubleshooter from '@/components/PrinterTroubleshooter';

const SAMPLE_RECEIPT = `
TIENDA DE PRUEBA
Calle Principal 123, Ciudad
+52 55 1234 5678

     RECIBO DE TRANSACCION
================================

Referencia:           WS-001-2024
Fecha:           01/12/24 - 14:30

--------------------------------
       DETALLE DE COMPRA
--------------------------------
Monto base:              $150.75
Intereses:                 $5.25
IVA:                      $24.12
--------------------------------
TOTAL:                   $180.12
================================

     INFORMACION DE PAGO
--------------------------------
Plazo:                   15 dias
Fecha limite:      16/12/24 - 14:30

    El cliente pagará después
  en el portal de Pagos de Propaga

================================
      Gracias por su compra
      Conserve este recibo
`;

export default function PrinterTestPage() {
  const [browserSupported, setBrowserSupported] = useState<boolean | null>(null);
  const [isRunningFullTest, setIsRunningFullTest] = useState(false);
  const [currentTest, setCurrentTest] = useState('');
  const [progress, setProgress] = useState(0);

  const {
    testResults,
    deviceInfo,
    isLoading,
    clearResults,
    checkWebUSBSupport,
    testWebUSB,
    detectDevices,
    testConnection,
    printReceiptUsingWebAPIprint,
    printTestReceipt,
  } = usePrinterTest();

  useEffect(() => {
    const isSupported = checkWebUSBSupport();
    setBrowserSupported(isSupported);
  }, [checkWebUSBSupport]);

  const handlePrintTest = async () => {
    await printTestReceipt(SAMPLE_RECEIPT);
  };

  const runFullTest = async () => {
    setIsRunningFullTest(true);
    setProgress(0);
    clearResults();

    const tests = [
      { name: 'Web USB Support', func: () => testWebUSB() },
      { name: 'Device Detection', func: () => detectDevices() },
      { name: 'Connection Test', func: () => testConnection() },
      { name: 'Print Test', func: () => printTestReceipt(SAMPLE_RECEIPT) },
    ];

    for (let i = 0; i < tests.length; i++) {
      const test = tests[i];
      setCurrentTest(`Running: ${test.name}...`);
      setProgress(((i + 1) / tests.length) * 100);

      await test.func();
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }

    setCurrentTest('All tests completed!');
    setIsRunningFullTest(false);
    setProgress(0);
  };

  const getResultColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'green.500';
      case 'error':
        return 'red.500';
      default:
        return 'blue.500';
    }
  };

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      default:
        return 'ℹ️';
    }
  };

  return (
    <Container maxW="6xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <Box textAlign="center">
          <Heading size="xl" mb={4}>
            🖨️ Epson Thermal Printer Test
          </Heading>
          <Text color="gray.600" fontSize="lg">
            Test your Epson thermal printer compatibility with Web USB API
          </Text>
        </Box>

        {/* Browser Compatibility */}
        <Card>
          <CardHeader>
            <Heading size="md">Browser Compatibility</Heading>
          </CardHeader>
          <CardBody>
            {browserSupported === null ? (
              <Text>Checking browser compatibility...</Text>
            ) : browserSupported ? (
              <Alert status="success">
                <AlertIcon />
                <Box>
                  <AlertTitle>✅ Web USB API supported</AlertTitle>
                  <AlertDescription>
                    Browser:{' '}
                    {typeof window !== 'undefined'
                      ? navigator.userAgent.split(' ').pop()
                      : 'Unknown'}
                  </AlertDescription>
                </Box>
              </Alert>
            ) : (
              <Alert status="error">
                <AlertIcon />
                <Box>
                  <AlertTitle>❌ Web USB API not supported</AlertTitle>
                  <AlertDescription>Please use Chrome, Edge, or Opera browser</AlertDescription>
                </Box>
              </Alert>
            )}
          </CardBody>
        </Card>
        {/* Printer Test Chrome API Printer */}
        <Card>
          <CardHeader>
            <Heading size="md">Printer Test Chrome API Printer</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              <Button
                colorScheme="blue"
                onClick={printReceiptUsingWebAPIprint}
                isLoading={isLoading}
                isDisabled={!browserSupported}
              >
                Test Web API Support
              </Button>
            </VStack>
          </CardBody>
        </Card>

        {/* Quick Tests */}
        <Card>
          <CardHeader>
            <Heading size="md">Quick Tests</Heading>
          </CardHeader>
          <CardBody>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4}>
              <Button
                colorScheme="blue"
                onClick={testWebUSB}
                isLoading={isLoading}
                isDisabled={!browserSupported}
              >
                Test Web USB Support
              </Button>
              <Button
                colorScheme="green"
                onClick={detectDevices}
                isLoading={isLoading}
                isDisabled={!browserSupported}
              >
                Detect Epson Devices
              </Button>
              <Button
                colorScheme="orange"
                onClick={testConnection}
                isLoading={isLoading}
                isDisabled={!browserSupported}
              >
                Test Connection
              </Button>
              <Button
                colorScheme="purple"
                onClick={handlePrintTest}
                isLoading={isLoading}
                isDisabled={!browserSupported}
              >
                Print Test Receipt
              </Button>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Full Test Suite */}
        <Card>
          <CardHeader>
            <Heading size="md">Full Test Suite</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              <Button
                colorScheme="red"
                size="lg"
                onClick={runFullTest}
                isLoading={isRunningFullTest}
                loadingText="Running Tests..."
                isDisabled={!browserSupported}
              >
                Run Complete Test
              </Button>

              {isRunningFullTest && (
                <Box>
                  <Text mb={2} fontSize="sm" color="gray.600">
                    {currentTest}
                  </Text>
                  <Progress value={progress} colorScheme="red" />
                </Box>
              )}
            </VStack>
          </CardBody>
        </Card>

        {/* Test Results */}
        {testResults.length > 0 && (
          <Card>
            <CardHeader>
              <Flex justify="space-between" align="center">
                <Heading size="md">Test Results</Heading>
                <Button size="sm" variant="outline" onClick={clearResults}>
                  Clear Results
                </Button>
              </Flex>
            </CardHeader>
            <CardBody>
              <VStack spacing={2} align="stretch" maxH="400px" overflowY="auto">
                {testResults.map((result, index) => (
                  <Box
                    key={index}
                    p={3}
                    bg="gray.50"
                    borderRadius="md"
                    borderLeft="4px solid"
                    borderLeftColor={getResultColor(result.type)}
                  >
                    <HStack>
                      <Text fontSize="sm">{getResultIcon(result.type)}</Text>
                      <Text fontSize="sm" color="gray.500">
                        {result.timestamp}:
                      </Text>
                      <Text fontSize="sm" flex="1">
                        {result.message}
                      </Text>
                    </HStack>
                  </Box>
                ))}
              </VStack>
            </CardBody>
          </Card>
        )}

        {/* Device Information */}
        {deviceInfo && (
          <Card>
            <CardHeader>
              <Heading size="md">Device Information</Heading>
            </CardHeader>
            <CardBody>
              <Alert status="info">
                <AlertIcon />
                <Box>
                  <AlertTitle>Connected Device</AlertTitle>
                  <VStack align="start" spacing={1} mt={2}>
                    <Text fontSize="sm">
                      <strong>Vendor ID:</strong> {deviceInfo.vendorId}
                    </Text>
                    <Text fontSize="sm">
                      <strong>Product ID:</strong> {deviceInfo.productId}
                    </Text>
                    <Text fontSize="sm">
                      <strong>Product Name:</strong> {deviceInfo.productName}
                    </Text>
                    <Text fontSize="sm">
                      <strong>Manufacturer:</strong> {deviceInfo.manufacturerName}
                    </Text>
                    <Text fontSize="sm">
                      <strong>Serial Number:</strong> {deviceInfo.serialNumber}
                    </Text>
                  </VStack>
                </Box>
              </Alert>
            </CardBody>
          </Card>
        )}

        {/* Sample Receipt Preview */}
        <Card>
          <CardHeader>
            <Heading size="md">Sample Receipt Preview</Heading>
          </CardHeader>
          <CardBody>
            <Code
              display="block"
              whiteSpace="pre"
              p={4}
              bg="gray.50"
              borderRadius="md"
              fontSize="xs"
              fontFamily="monospace"
              overflowX="auto"
            >
              {SAMPLE_RECEIPT}
            </Code>
          </CardBody>
        </Card>

        {/* Advanced Troubleshooter */}
        <PrinterTroubleshooter />

        {/* Quick Troubleshooting Guide */}
        <Card>
          <CardHeader>
            <Heading size="md">Troubleshooting Guide</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              <Alert status="info">
                <AlertIcon />
                <Box>
                  <AlertTitle>Browser Requirements</AlertTitle>
                  <AlertDescription>
                    This test requires a browser that supports Web USB API (Chrome, Edge, or Opera).
                    Make sure your Epson printer is connected via USB.
                  </AlertDescription>
                </Box>
              </Alert>

              <Box>
                <Text fontWeight="bold" mb={2}>
                  Common Issues:
                </Text>
                <VStack spacing={3} align="start" pl={4}>
                  <Box>
                    <Text fontWeight="semibold" color="red.600">
                      Web USB not supported:
                    </Text>
                    <Text fontSize="sm">
                      Use Chrome, Edge, or Opera browser. Firefox and Safari don't support Web USB.
                    </Text>
                  </Box>
                  <Box>
                    <Text fontWeight="semibold" color="orange.600">
                      No devices found:
                    </Text>
                    <Text fontSize="sm">
                      • Check USB cable connection
                      <br />
                      • Ensure printer is powered on
                      <br />
                      • Try a different USB port
                      <br />• Check if printer drivers are installed
                    </Text>
                  </Box>
                  <Box>
                    <Text fontWeight="semibold" color="yellow.600">
                      Connection failed:
                    </Text>
                    <Text fontSize="sm">
                      • Close other applications using the printer
                      <br />
                      • Restart the printer
                      <br />• Try refreshing the browser page
                    </Text>
                  </Box>
                  <Box>
                    <Text fontWeight="semibold" color="green.600">
                      Supported Epson Models:
                    </Text>
                    <Code fontSize="xs">
                      TM-T20, TM-T82, TM-T88, TM-U220, TM-H6000, and most ESC/POS compatible models
                    </Code>
                  </Box>
                </VStack>
              </Box>
            </VStack>
          </CardBody>
        </Card>
      </VStack>
    </Container>
  );
}
